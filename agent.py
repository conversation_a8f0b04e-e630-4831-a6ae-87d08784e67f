import sqlite3
import os
from gtts import gTTS
import speech_recognition as sr

def setup_database():
    conn = sqlite3.connect('example.db')
    cursor = conn.cursor()

    # Always recreate the table to avoid schema mismatch
    cursor.execute('DROP TABLE IF EXISTS users')

    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            balance REAL,
            debt REAL,
            discount REAL,
            birthday TEXT,
            card_number TEXT,
            due_date TEXT,
            discount_due_date TEXT 
        )
    ''')

    sample_users = [
        ("Alice", 1000.0, 200.0, 10.0, "1990-01-01", "1234100", "2025-08-31", "2025-08-25"),
        ("Bob", 1500.0, 100.0, 5.0, "1985-05-12", "9876543211", "2025-08-31", "2025-08-25"),
        ("Charlie", 500.0, 50.0, 2.5, "1992-07-23", "5555", "2025-08-31", "2025-08-25"),
    ]

    cursor.executemany('''
        INSERT INTO users (name, balance, debt, discount, birthday, card_number, due_date, discount_due_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', sample_users)

    conn.commit()
    return conn




def find_user_by_card_last_digit(last_digit):
    conn = sqlite3.connect('example.db')
    conn.row_factory = sqlite3.Row   # ✅ return rows as dict-like objects
    cursor = conn.cursor()
    query = '''
        SELECT name, balance, debt, discount, birthday, card_number, due_date, discount_due_date
        FROM users
        WHERE card_number LIKE ?
    '''
    cursor.execute(query, ('%' + str(last_digit),))
    results = cursor.fetchall()
    conn.close()
    return results

def speak(text):
    tts = gTTS(text)
    tts.save("output.mp3")
    os.system("mpg123 output.mp3 > /dev/null 2>&1")
    os.remove("output.mp3")

if __name__ == "__main__":
    setup_database()
    company_name = "Alexis A Molaer Law offices"
    # 1. Welcome Prompt
    welcome = f"Thank you for calling {company_name}. Your calling outside our business hours we will be passing you to our automated Servicedesk. This is our automated Servicedesk Assistant. I'll help you with your account today."
    print(welcome)
    speak(welcome)

    # 2. Identity Verification
    while True:
        speak("For us to find your record Please enter your Card Number.")
        card_number = input("Enter your Card Number: ")
        speak("For verification, please enter your birth year.")
        birthyear = input("Enter your birth year (YYYY): ")

        conn = sqlite3.connect('example.db')
        conn.row_factory = sqlite3.Row   # ✅ use dict-style access
        cursor = conn.cursor()
        cursor.execute("""
            SELECT name, balance, debt, discount, birthday, card_number, due_date, discount_due_date
            FROM users WHERE card_number LIKE ?
        """, ('%' + card_number,)) 
        user = cursor.fetchone()
        conn.close()

        match = False
        if user:
            db_year = user["birthday"].split('-')[0]
            print(f"[DEBUG] Input birth year: {birthyear}, DB birth year: {db_year}, Card: {user['card_number']}")
            if birthyear == db_year:
                match = True

        if match:
            speak("Thank you. I've successfully verified your account.")
            print("Thank you. I've successfully verified your account.")
            break
        else:
            speak("I'm sorry, the details you provided do not match our records. Let's try again.")
            print("I'm sorry, the details you provided do not match our records. Let's try again.")

    # 3. Personalized Response
    first_name = user["name"].split()[0]
    speak(f"Hi {first_name}, thank you for verifying your account. Here are your details:")
    print(f"Hi {first_name}, thank you for verifying your account. Here are your details:")
    speak(f"Your current balance is {user['balance']} pesos.")
    print(f"Your current balance is {user['balance']} pesos.")

    due_date = user["due_date"]
    discount_due_date = user["discount_due_date"]
    speak(f"Your payment is due on {due_date}.")
    print(f"Your payment is due on {due_date}.")
    speak(f"You are eligible for a settlement discount of {user['discount']} pesos if paid before {discount_due_date}.")
    print(f"You are eligible for a settlement discount of {user['discount']} pesos if paid before {discount_due_date}.")
    speak("You may pay via Bank Transfer, GCash, Maya, or at our partner payment centers.")
    print("You may pay via Bank Transfer, GCash, Maya, or at our partner payment centers.")

    # 4. Follow-up Options
    speak("Would you like me to send the payment details to your registered mobile number?")
    print("Would you like me to send the payment details to your registered mobile number?")
    speak("If you would like to speak with a representative, please say Agent or press 0.")
    print("If you would like to speak with a representative, please say Agent or press 0.")
    speak("If you would like to hear this information again, press 1.")
    print("If you would like to hear this information again, press 1.")

    follow_up = input("Enter your choice (0 for Agent, 1 to repeat, any other key to finish): ")
    if follow_up == "1":
        speak(f"Hi {first_name}, thank you for verifying your account. Here are your details:")
        print(f"Hi {first_name}, thank you for verifying your account. Here are your details:")
        speak(f"Your current balance is {user['balance']} pesos.")
        print(f"Your current balance is {user['balance']} pesos.")
        speak(f"Your payment is due on {due_date}.")
        print(f"Your payment is due on {due_date}.")
        speak(f"You are eligible for a settlement discount of {user['discount']} pesos if paid before {discount_due_date}.")
        print(f"You are eligible for a settlement discount of {user['discount']} pesos if paid before {discount_due_date}.")
        speak("You may pay via Bank Transfer, GCash, Maya, or at our partner payment centers.")
        print("You may pay via Bank Transfer, GCash, Maya, or at our partner payment centers.")
    elif follow_up == "0":
        speak("Connecting you to a representative. Please wait.")
        print("Connecting you to a representative. Please wait.")
    else:
        speak("Payment details will be sent to your registered mobile number.")
        print("Payment details will be sent to your registered mobile number.")

    # 5. Closing Prompt
    speak(f"Thank you, {first_name}. We appreciate your time. Have a great day!")
    print(f"Thank you, {first_name}. We appreciate your time. Have a great day!")
